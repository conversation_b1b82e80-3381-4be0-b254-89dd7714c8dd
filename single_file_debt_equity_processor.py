"""
Single File Debt-Equity Processor with AI-Based Value Conversion (Enhanced with Universal Header Context)
Updated with OpenAI for Enhanced Accuracy - AI handles all unit conversions with intelligent header detection
Author: Gangatharangurusamy
Date: 2025-07-21 12:19:56
Purpose: Process single financial document with adaptive fallback, AI does all math with universal currency support
"""

import os
import json
import re
import base64
from typing import List, Dict, Optional, Tuple, Union
from pathlib import Path
import logging
from datetime import datetime
from dataclasses import dataclass, asdict

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed")

# Required imports - Updated for OpenAI
try:
    import openai
    from openai import OpenAI
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install: pip install openai")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('single_file_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FinalDebtEquityData:
    """Final data structure - ONLY 2 fields as required"""
    debt_equity_analysis: List[Dict] = None
    equity_liability: Dict = None
    
    def __post_init__(self):
        if self.debt_equity_analysis is None:
            self.debt_equity_analysis = []
        
        if self.equity_liability is None:
            self.equity_liability = {
                "date": "",
                "currency_unit": "",
                "equity": {
                    "equity_share_capital": [{"name": "", "no_of_shares": None, "value": None}],
                    "other_equity": [{"name": "", "value": None}],
                    "roe": None
                },
                "long_term_debt": {
                    "interest": "",
                    "junior_debt": {
                        "investor_details": [{"name": "", "type": "", "value": None}],
                        "value": None
                    },
                    "senior_debt": {
                        "investor_details": [{"name": "", "type": "", "value": None}],
                        "value": None
                    }
                },
                "others": {
                    "others": [{"name": "", "value": None}]
                },
                "short_term_debt": {
                    "interest": "",
                    "junior_debt": {
                        "investor_details": [{"name": "", "type": "", "value": None}],
                        "value": None
                    },
                    "senior_debt": {
                        "investor_details": [{"name": "", "type": "", "value": None}],
                        "value": None
                    }
                }
            }

class UniversalCurrencyConfig:
    """Universal currency and unit configuration for all countries"""
    
    GLOBAL_CURRENCY_UNITS = {
        # 🇮🇳 INDIA - Multiple unit systems
        "INR": {
            "symbols": ["₹", "Rs.", "INR", "Rupees", "Indian Rupees", "Rs"],
            "traditional_units": {
                "crore": 10000000,
                "crores": 10000000,
                "cr": 10000000,
                "lakh": 100000,
                "lakhs": 100000,
                "l": 100000
            },
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts are in ₹Crores",
                "Figures in ₹ Lakhs",
                "Amount in ₹ Millions",
                "Currency in ₹ Thousands"
            ]
        },
        
        # 🇿🇦 SOUTH AFRICA
        "ZAR": {
            "symbols": ["R", "ZAR", "Rand", "Rm", "South African Rand"],
            "traditional_units": {
                "rm": 1000000,  # Rand millions
                "rand millions": 1000000,
                "r millions": 1000000
            },
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "Amount in Rand Millions",
                "All figures in R Millions",
                "Currency in Rm",
                "Figures in R'000"
            ]
        },
        
        # 🇺🇸 UNITED STATES
        "USD": {
            "symbols": ["$", "USD", "US$", "Dollar", "Dollars", "US Dollar"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in USD Millions",
                "Figures in $ Thousands",
                "Currency in US$ Billions",
                "Amount in $'000"
            ]
        },
        
        # 🇬🇧 UNITED KINGDOM
        "GBP": {
            "symbols": ["£", "GBP", "Pounds", "Sterling", "British Pounds"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in £ Millions",
                "Figures in £'000",
                "Currency in £ Thousands",
                "Amount in GBP Millions"
            ]
        },
        
        # 🇪🇺 EUROPE
        "EUR": {
            "symbols": ["€", "EUR", "Euro", "Euros"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in € Millions",
                "Figures in EUR Thousands",
                "Currency in € Billions",
                "Amount in €'000"
            ]
        },
        
        # 🇯🇵 JAPAN
        "JPY": {
            "symbols": ["¥", "JPY", "Yen", "Japanese Yen"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in ¥ Millions",
                "Figures in JPY Billions",
                "Currency in ¥ Thousands"
            ]
        },
        
        # 🇨🇳 CHINA
        "CNY": {
            "symbols": ["¥", "CNY", "Yuan", "RMB", "Chinese Yuan"],
            "traditional_units": {
                "亿": *********,  # Yi (100 million)
                "万": 10000,      # Wan (10 thousand)
                "yi": *********,
                "wan": 10000
            },
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in ¥ Millions",
                "Figures in CNY 万",
                "Currency in RMB 亿"
            ]
        },
        
        # 🇦🇺 AUSTRALIA
        "AUD": {
            "symbols": ["A$", "AUD", "Australian Dollar", "AU$"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in A$ Millions",
                "Figures in AUD Thousands"
            ]
        },
        
        # 🇨🇦 CANADA
        "CAD": {
            "symbols": ["C$", "CAD", "Canadian Dollar", "CA$"],
            "traditional_units": {},
            "international_units": {
                "trillion": *********0000,
                "billion": *********0,
                "million": 1000000,
                "thousand": 1000,
                "hundreds": 100
            },
            "common_patterns": [
                "All amounts in C$ Millions",
                "Figures in CAD Thousands"
            ]
        }
    }
    
    @classmethod
    def get_all_currency_symbols(cls) -> List[str]:
        """Get all possible currency symbols for detection"""
        symbols = []
        for currency_data in cls.GLOBAL_CURRENCY_UNITS.values():
            symbols.extend(currency_data["symbols"])
        return symbols
    
    @classmethod
    def get_all_units(cls) -> Dict[str, int]:
        """Get all possible units with their multipliers"""
        all_units = {}
        for currency_data in cls.GLOBAL_CURRENCY_UNITS.values():
            all_units.update(currency_data["traditional_units"])
            all_units.update(currency_data["international_units"])
        return all_units

class SingleFileDebtEquityProcessor:
    """Process single financial document with adaptive fallback strategy using OpenAI - Enhanced with universal header context detection"""
    
    # 🎯 ADAPTIVE PRIORITY ORDER
    STATEMENT_PRIORITY = [
        "Statement of Balance Sheet",
        "Statement of Profit and Loss", 
        "Statement of Changes in Equity",
        "Statement of Cash Flows",
        "Notes to Financial Statements"
    ]

    def __init__(self):
        # Initialize OpenAI client
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found. Please set it in .env file")
        
        self.client = OpenAI(api_key=api_key)
        
        # OpenAI model configuration for enhanced accuracy
        self.model = "gpt-4o"  # Most accurate model for vision tasks
        self.max_tokens = 4000
        self.temperature = 0  # For consistent, accurate results
        
        # Initialize universal currency config
        self.currency_config = UniversalCurrencyConfig()
        
        logger.info("Single File Debt-Equity Processor with OpenAI initialized")
        logger.info(f"Using model: {self.model} with temperature: {self.temperature}")
        logger.info("🌍 Universal header-context detection enabled for all countries")
        logger.info("📅 AI-based year detection from document content enabled")

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def safe_numeric_value(self, value) -> Optional[float]:
        """Safely extract numeric value preserving decimals - AI should provide final values"""
        if value is None:
            return None

        if isinstance(value, (int, float)):
            return float(value) if value != 0 else None

        if isinstance(value, str):
            try:
                # Remove any currency symbols and commas
                clean_value = re.sub(r'[₹$€£¥R,A-Za-z]', '', str(value)).strip()
                return float(clean_value) if clean_value and float(clean_value) != 0 else None
            except ValueError:
                return None
        
        return None

    def get_primary_financial_statement(self, classifications: Dict) -> Tuple[Optional[str], Optional[Dict]]:
        """🎯 ADAPTIVE: Get best available financial statement for analysis"""
        print("🔍 Adaptive Strategy: Finding best available financial statement...")
        
        for statement_type in self.STATEMENT_PRIORITY:
            if statement_type in classifications:
                statement_info = classifications[statement_type]
                if statement_info.get('full_paths'):  # Make sure it has actual images
                    logger.info(f"✅ Found: {statement_type} with {len(statement_info['full_paths'])} images")
                    print(f"🎯 Using: {statement_type} as primary source")
                    return statement_type, statement_info
        
        logger.warning("❌ No financial statements found in classifications")
        return None, None

    async def intelligent_year_detection(self, primary_images: List[str], statement_type: str) -> int:
        """📅 AI-BASED: Extract actual financial year from document content"""
        print(f"📅 Step 0: AI-based year detection from {statement_type} content...")

        year_detection_prompt = f"""
        Extract the ACTUAL FINANCIAL YEAR from this {statement_type} document content.

        🔍 LOOK FOR YEAR INDICATORS IN:

        1. BALANCE SHEET HEADERS:
        - "Balance Sheet as at 31 March 2024" → Extract: 2024
        - "As at March 31, 2024" → Extract: 2024
        - "Statement of Financial Position as at 31st March 2024" → Extract: 2024

        2. PROFIT & LOSS HEADERS:
        - "For the year ended 31st March 2024" → Extract: 2024
        - "Statement of Profit and Loss for the year ended March 31, 2024" → Extract: 2024
        - "Income Statement for the year ended 31 March 2024" → Extract: 2024

        3. DOCUMENT TITLES:
        - "Annual Report 2024" → Extract: 2024
        - "Financial Year 2023-24" → Extract: 2024 (ALWAYS TAKE THE ENDING YEAR)
        - "Year Ended March 31, 2024" → Extract: 2024
        - "KSK Mahanadi Power Company Limited - Annual Report 2024" → Extract: 2024

        4. CASH FLOW HEADERS:
        - "Statement of Cash Flows for the year ended 31 March 2024" → Extract: 2024

        5. EQUITY CHANGE HEADERS:
        - "Statement of Changes in Equity for the year ended 31 March 2024" → Extract: 2024

        📋 CRITICAL EXTRACTION RULES:
        - ALWAYS take the ENDING YEAR from date ranges (2024 from "2023-24", 2024 from "Year Ended March 31, 2024")
        - Look for the most prominent year mentioned in headers/titles
        - Prefer "as at" dates and "for the year ended" dates
        - Year should be between 2000-2030
        - If multiple years found, take the main financial year (the ending year)

        Return ONLY this JSON format:
        {{
            "financial_year": 2024,
            "found_in": "Year Ended March 31, 2024",
            "confidence": "high"
        }}

        Be precise - extract the actual financial year from document content, ALWAYS taking the ending year from ranges.
        """
        
        try:
            # Prepare messages for OpenAI
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": year_detection_prompt}
                    ]
                }
            ]
            
            # Add first 2 images for year detection (usually contains headers)
            for i, image_path in enumerate(primary_images[:2]):
                image_base64 = self.encode_image_base64(image_path)
                messages[0]["content"].extend([
                    {"type": "text", "text": f"\n{statement_type} Page {i + 1} (for year detection):"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}",
                            "detail": "high"
                        }
                    }
                ])
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=500,
                temperature=self.temperature
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            year_data = json.loads(response_text)
            extracted_year = year_data.get('financial_year')
            found_in = year_data.get('found_in', 'document content')
            confidence = year_data.get('confidence', 'medium')
            
            # Validate extracted year
            if extracted_year and isinstance(extracted_year, int) and 2000 <= extracted_year <= 2030:
                logger.info(f"📅 AI detected year: {extracted_year} from '{found_in}' (confidence: {confidence})")
                print(f"📅 Financial year detected: {extracted_year}")
                return extracted_year
            else:
                logger.warning(f"⚠️ Invalid year detected: {extracted_year}, using current year")
                return 2025  # Current year fallback
            
        except Exception as e:
            logger.error(f"Error in AI year detection: {e}")
            print(f"⚠️ Year detection failed, using current year: 2025")
            return 2025  # Current year fallback
    async def intelligent_header_context_detection(self, primary_images: List[str], statement_type: str) -> Dict:
        """🧠 INTELLIGENT: Detect header context for any currency/unit combination"""
        print(f"🧠 Step 1: Intelligent header context detection from {statement_type}...")
        
        # Get all possible symbols and units for detection
        all_symbols = self.currency_config.get_all_currency_symbols()
        all_units = self.currency_config.get_all_units()
        
        detection_prompt = f"""
        🧠 INTELLIGENT HEADER CONTEXT DETECTION

        Analyze this financial document to find the unit declaration in headers, footnotes, or anywhere in the document.

        🔍 LOOK FOR DECLARATIONS LIKE:
        - "All amounts are in ₹Crores, unless otherwise stated"
        - "Figures in USD Millions"
        - "Amount in Rand Millions (Rm)"
        - "Currency in ₹ Lakhs"
        - "All figures in £'000"
        - "Values in € Billions"
        - "Amount in ¥ Millions"
        - "Figures in A$ Thousands"
        - "Currency: CNY 万"
        - "All amounts in C$ Millions"

        🌍 SUPPORTED CURRENCIES: {all_symbols}
        
        🔢 SUPPORTED UNITS:
        - Crores (10,000,000)
        - Lakhs (100,000) 
        - Millions (1,000,000)
        - Billions (1,000,000,000)
        - Trillions (1,000,000,000,000)
        - Thousands (1,000)
        - Hundreds (100)
        - Rm/Rand Millions (1,000,000)
        - 万/Wan (10,000)
        - 亿/Yi (100,000,000)

        📋 FIND THE DECLARATION:
        1. Look in document header
        2. Look in footnotes
        3. Look near table headers
        4. Look in any explanatory text

        Return ONLY this JSON format:
        {{
            "declaration_found": "All amounts are in ₹Crores, unless otherwise stated",
            "currency": "INR",
            "currency_symbol": "₹",
            "base_unit": "crores",
            "multiplier": 10000000,
            "confidence": "high",
            "location": "document header"
        }}

        If multiple declarations exist, prioritize the main one for financial figures.
        If no clear declaration found, make best estimate from context.
        """
        
        try:
            # Prepare messages for OpenAI
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": detection_prompt}
                    ]
                }
            ]
            
            # Add primary statement images
            for i, image_path in enumerate(primary_images):
                image_base64 = self.encode_image_base64(image_path)
                messages[0]["content"].extend([
                    {"type": "text", "text": f"\n{statement_type} Page {i + 1}:"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}",
                            "detail": "high"
                        }
                    }
                ])
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=1000,
                temperature=self.temperature
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            context_data = json.loads(response_text)
            
            logger.info(f"🧠 Header context detected: {context_data.get('declaration_found', 'N/A')}")
            logger.info(f"💰 Currency: {context_data.get('currency', 'N/A')} | Unit: {context_data.get('base_unit', 'N/A')} | Multiplier: {context_data.get('multiplier', 'N/A')}")
            
            return context_data
            
        except Exception as e:
            logger.error(f"Error in header context detection: {e}")
            # Return default context
            return {
                "declaration_found": "Unable to detect declaration",
                "currency": "INR",
                "currency_symbol": "₹",
                "base_unit": "crores",
                "multiplier": 10000000,
                "confidence": "low",
                "location": "default assumption"
            }

    async def detect_currency_only(self, primary_images: List[str], statement_type: str) -> str:
        """🔄 ENHANCED: Detect currency with universal support"""
        print(f"🔍 Step 1: Detecting currency from {statement_type}...")
        
        # Use intelligent header context detection
        header_context = await self.intelligent_header_context_detection(primary_images, statement_type)
        currency = header_context.get('currency', 'INR')
        
        logger.info(f"✅ Detected currency: {currency}")
        return currency

    async def get_important_notes_adaptive(self, primary_images: List[str], statement_type: str) -> List[str]:
        """🎯 ADAPTIVE: Get important notes from any financial statement using OpenAI"""
        print(f"🎯 Adaptive Note Selection: Getting important debt/equity notes from {statement_type}...")
        
        smart_notes_prompt = f"""
        Analyze this {statement_type} and identify the most important note numbers for comprehensive debt and equity analysis.
        
        {"Since this is a Balance Sheet, look for notes referenced next to key items:" if statement_type == "Statement of Balance Sheet" else ""}
        {"Since this is a Profit & Loss Statement, look for notes about financing and equity items:" if statement_type == "Statement of Profit and Loss" else ""}
        {"Since this is a Cash Flow Statement, look for notes about financing activities:" if statement_type == "Statement of Cash Flows" else ""}
        {"Since this is Changes in Equity, look for notes explaining equity components:" if statement_type == "Statement of Changes in Equity" else ""}
        
        **PRIORITY ITEMS TO FIND NOTES FOR:**
        - Equity Share Capital → Note ?
        - Other Equity/Retained Earnings → Note ?
        - Borrowings/Debt → Note ?
        - Long-term liabilities → Note ?
        - Current liabilities → Note ?
        - Share capital details → Note ?
        
        Return the 6-8 most important note numbers that contain detailed debt and equity information.
        
        Return ONLY this JSON format:
        {{
            "important_notes": ["13", "14", "15", "16", "17", "18"],
            "reasoning": "Notes identified from {statement_type} for debt and equity analysis"
        }}
        
        Extract only the note NUMBERS (like "13", "14").
        """
        
        try:
            # Prepare messages for OpenAI
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": smart_notes_prompt}
                    ]
                }
            ]
            
            # Add primary statement images
            for i, image_path in enumerate(primary_images):
                image_base64 = self.encode_image_base64(image_path)
                messages[0]["content"].extend([
                    {"type": "text", "text": f"\n{statement_type} Page {i + 1}:"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}",
                            "detail": "high"
                        }
                    }
                ])
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            notes_data = json.loads(response_text)
            important_notes = notes_data.get('important_notes', [])
            
            # Clean note numbers
            cleaned_notes = []
            for note in important_notes:
                clean_note = str(note).replace('Note ', '').replace('note ', '').replace('NOTE ', '').strip()
                digits_only = ''.join(filter(str.isdigit, clean_note))
                if digits_only:
                    cleaned_notes.append(digits_only)
                elif clean_note and not clean_note.isdigit():
                    cleaned_notes.append(str(note))
            # Remove duplicates while preserving order
            final_notes = []
            for note in cleaned_notes:
                if note not in final_notes:
                    final_notes.append(note)
            
            logger.info(f"🎯 Adaptive selection from {statement_type}: {final_notes}")
            
            # Fallback to common notes if none found
            if not final_notes:
                final_notes = ['13', '14', '15', '16', '17', '18']
                logger.info("⚠️ Using fallback notes")
            
            return final_notes
            
        except Exception as e:
            logger.error(f"Error in adaptive note selection from {statement_type}: {e}")
            fallback_notes = ['13', '14', '15', '16', '17', '18']
            logger.info(f"⚠️ Using fallback notes: {fallback_notes}")
            return fallback_notes

    def get_ai_conversion_prompt(self, statement_type: str, currency: str, important_notes: List[str]) -> str:
        """🌍 UNIVERSAL FINANCIAL EXTRACTION: Extract raw numbers and unit information separately"""

        base_instructions = f"""
        🌍 UNIVERSAL FINANCIAL EXTRACTION WITH UNIT DETECTION

        🚨 CRITICAL INSTRUCTION FOR UNIT DETECTION:
        - Look for unit declarations in document headers like:
          * "(All amounts are in ₹ Crores, unless otherwise specified)" → extract "Crores"
          * "Figures in ₹ Lakhs" → extract "Lakhs"
          * "Amount in USD Millions" → extract "Millions"
          * "Currency in ₹ Thousands" → extract "Thousands"
        - Return ONLY the unit name (Crores/Lakhs/Millions/Thousands/Billions) in currency_unit field
        - Extract all numbers as they appear in the document WITHOUT any conversion

        🚨 CRITICAL INSTRUCTION FOR DATE FORMAT:
        - Always return dates in DD/MM/YYYY format (e.g., "31/12/2024")
        - Examples: "31st March 2024" → "31/03/2024", "March 31, 2024" → "31/03/2024"

        🚨 CRITICAL INSTRUCTION FOR YEAR EXTRACTION:
        - ALWAYS extract the ENDING YEAR from date ranges
        - "Year Ended March 31, 2024" → Extract: 2024
        - "Financial Year 2023-24" → Extract: 2024 (take the ending year)
        - "For the year ended 31st March 2024" → Extract: 2024

        🚨 CRITICAL INSTRUCTION FOR GROUP vs COMPANY DATA:
        - If the statement shows BOTH "Group" and "Company" columns, ALWAYS extract from "Company" columns ONLY
        - Group data should be ignored completely

        🚨 CRITICAL DEBT CATEGORIZATION RULES FOR debt_equity_analysis:
        - LONG-TERM DEBT: Include ONLY "Non-current borrowings" - EXCLUDE all other non-current liabilities
        - SHORT-TERM DEBT: Include ONLY "Current borrowings" and "Current maturities of long-term debt" - EXCLUDE all other current liabilities
        - OTHERS: Include ALL other current and non-current liabilities (trade payables, provisions, other liabilities, etc.) - EXCLUDE borrowings

        🚨 CRITICAL DETAILED EXTRACTION FROM NOTES OR BALANCE SHEET:
        - For senior_debt and junior_debt, extract ALL INDIVIDUAL LINE ITEMS from notes sections
        - If detailed notes are NOT available, extract from balance sheet line items
        - DO NOT return null values - always extract available data from balance sheet
        - Extract numbers exactly as they appear in the document
        - Example from balance sheet: "Non-current borrowings: 27,785.40" → extract as 27785.40
        - Example from notes: Extract "Term Loan Bank A: 400", "Term Loan Bank B: 300", "Debentures: 300"

        IMPORTANT CONTEXT:
        - Currency: {currency}
        - Intelligently selected notes for analysis: {important_notes}
        - PRIMARY SOURCE: {statement_type}
        - Extract raw numbers without any mathematical conversion
        """

        if statement_type == "Statement of Balance Sheet":
            specific_instructions = """
            EXTRACT FROM BALANCE SHEET (Company data only) with UNIT DETECTION:

            1. BASIC INFORMATION:
            - Company name
            - Balance sheet date (As at...) - FORMAT AS DD/MM/YYYY
            - Financial year (ALWAYS take ending year from ranges like "2023-24" → 2024)

            2. UNIT DETECTION:
            - Look for unit declarations like "All amounts are in ₹Crores" → extract "Crores"
            - Return ONLY the unit name in currency_unit field
            - Extract all numbers as they appear WITHOUT conversion

            3. EQUITY ANALYSIS (Company columns only) - RAW NUMBERS:
            - Equity Share Capital (extract raw number, get number of shares AND face value per share from notes)
            - Face Value Per Share: Extract from notes sections (e.g., "₹10 per share", "₹100 per share")
            - Other Equity components (extract raw numbers for each)
            - Total Equity (extract raw number from Company column)

            4. LONG-TERM DEBT ANALYSIS (Company columns only) - RAW NUMBERS:
            - For debt_equity_analysis: ONLY include "Non-current borrowings" - EXCLUDE all other non-current liabilities
            - For equity_liability details: Extract ALL INDIVIDUAL ITEMS from notes OR balance sheet
            - If notes not available, extract from balance sheet line items under "Non-current borrowings"
            - Classify as senior_debt (secured) or junior_debt (unsecured)
            - DO NOT return null - always extract available data
            - Extract numbers exactly as they appear in document

            5. SHORT-TERM DEBT ANALYSIS (Company columns only) - RAW NUMBERS:
            - For debt_equity_analysis: ONLY include "Current borrowings" and "Current maturities of long-term debt" - EXCLUDE all other current liabilities
            - For equity_liability details: Extract ALL INDIVIDUAL ITEMS from notes OR balance sheet with detailed breakdown:
              * Senior debt (secured): Working capital loans, Cash credit, Bank overdrafts, Secured short-term loans
              * Junior debt (unsecured): Current maturities of long-term debt, Unsecured loans, Related party loans, Commercial paper
            - If notes not available, extract from balance sheet line items under "Current borrowings"
            - Classify each item as senior_debt (secured) or junior_debt (unsecured) with lender details
            - Extract interest rates for short-term facilities
            - DO NOT return null - always extract available data
            - Extract numbers exactly as they appear in document

            6. OTHER LIABILITIES (Company columns only) - RAW NUMBERS:
            - Extract BOTH current and non-current liabilities separately for aggregation
            - Trade payables (current + non-current), Provisions (current + non-current), Deferred tax liabilities, etc.
            - These should NOT be included in long-term or short-term debt categories
            - Extract numbers exactly as they appear in document
            - Separate current and non-current items for proper aggregation
            """

        elif statement_type == "Statement of Profit and Loss":
            specific_instructions = """
            EXTRACT FROM PROFIT & LOSS STATEMENT (Company data only) with UNIT DETECTION:

            1. BASIC INFORMATION:
            - Company name
            - Period (For the year ended...) - FORMAT AS DD/MM/YYYY
            - Financial year

            2. UNIT DETECTION:
            - Look for unit declarations in document header → extract unit name only
            - Return ONLY the unit name in currency_unit field
            - Extract all numbers as they appear WITHOUT conversion

            3. EQUITY ANALYSIS (derive from P&L) - RAW NUMBERS:
            - Look for retained earnings movement (extract raw number)
            - Share capital references (extract raw number)
            - Dividend payments (extract raw number)

            4. DEBT-RELATED ANALYSIS - RAW NUMBERS:
            - Finance costs/Interest expenses (extract raw number)
            - Any borrowing cost capitalization (extract raw number)
            """

        elif statement_type == "Statement of Changes in Equity":
            specific_instructions = """
            EXTRACT FROM CHANGES IN EQUITY (Company data only) with UNIT DETECTION:

            1. BASIC INFORMATION:
            - Company name
            - Period (For the year ended...) - FORMAT AS DD/MM/YYYY
            - Financial year

            2. UNIT DETECTION:
            - Look for unit declarations in document header → extract unit name only
            - Return ONLY the unit name in currency_unit field
            - Extract all numbers as they appear WITHOUT conversion

            3. EQUITY ANALYSIS (Primary focus) - RAW NUMBERS:
            - Opening/Closing balance of Share Capital (extract raw number)
            - Number of shares (actual numbers)
            - Other Equity components (extract raw numbers for each)
            - Retained earnings movement (extract raw number)

            4. EQUITY TRANSACTIONS - RAW NUMBERS:
            - New share issues (extract raw number)
            - Dividend payments (extract raw number)
            """

        elif statement_type == "Statement of Cash Flows":
            specific_instructions = """
            EXTRACT FROM CASH FLOW STATEMENT (Company data only) with UNIT DETECTION:

            1. BASIC INFORMATION:
            - Company name
            - Period (For the year ended...) - FORMAT AS DD/MM/YYYY
            - Financial year

            2. UNIT DETECTION:
            - Look for unit declarations in document header → extract unit name only
            - Return ONLY the unit name in currency_unit field
            - Extract all numbers as they appear WITHOUT conversion

            3. FINANCING ACTIVITIES FOCUS - RAW NUMBERS:
            - Proceeds from borrowings (extract raw number)
            - Repayment of borrowings (extract raw number)
            - Proceeds from share capital (extract raw number)
            - Dividend payments (extract raw number)
            """

        else:  # Notes to Financial Statements
            specific_instructions = """
            EXTRACT FROM NOTES TO FINANCIAL STATEMENTS with UNIT DETECTION:

            1. BASIC INFORMATION:
            - Company name
            - Financial year

            2. UNIT DETECTION:
            - Look for unit declarations in notes section → extract unit name only
            - Return ONLY the unit name in currency_unit field
            - Extract all numbers as they appear WITHOUT conversion

            3. DETAILED BREAKDOWNS - RAW NUMBERS:
            - Share capital details (extract raw numbers)
            - Borrowings classification (extract raw numbers)
            - Interest rates (as percentages)
            """

        return f"""
        You are analyzing financial statements to extract comprehensive debt and equity information with maximum accuracy.
        You MUST detect unit information and extract raw numbers without conversion!

        {base_instructions}

        {specific_instructions}

        🎯 EXTRACT RAW NUMBERS AND UNIT INFORMATION!
        Look for declarations like "All amounts are in ₹Crores" and return "Crores" in currency_unit field!
        Extract all numbers exactly as they appear in the document!

        If any information is not available, return as null.

        Return as JSON with RAW NUMBERS and UNIT INFORMATION:
        {{
            "company_info": {{
                "name": "Company Name",
                "date": "31/03/2024",
                "year": "2024"
            }},
            "currency_unit": "Crores",
            "equity": {{
                "share_capital": {{
                    "name": "Equity Share Capital",
                    "value": 200.00,
                    "shares": 20000000,
                    "face_value_per_share": 10
                }},
                "other_equity": [
                    {{"name": "Securities Premium", "value": 1126.44}},
                    {{"name": "Retained Earnings", "value": 543.21}}
                ],
                "total_equity": 200.00
            }},
            "long_term_debt": {{
                "total_value": 2778.54,
                "interest_rates": "8.5-9.5%",
                "secured_debt": {{
                    "total": 2778.54,
                    "details": [
                        {{"name": "Non-current borrowings", "amount": 2778.54, "lender": "Banks/Financial Institutions", "security": "secured"}}
                    ]
                }},
                "unsecured_debt": {{
                    "total": 0,
                    "details": []
                }}
            }},
            "short_term_debt": {{
                "total_value": 1079.20,
                "interest_rates": "8.0-9.0%",
                "current_borrowings": 836.10,
                "current_maturities": 243.10,
                "secured_debt": {{
                    "total": 836.10,
                    "details": [
                        {{"name": "Working Capital Loan - Bank A", "amount": 400.00, "lender": "Bank A", "security": "secured"}},
                        {{"name": "Cash Credit Facility - Bank B", "amount": 300.00, "lender": "Bank B", "security": "secured"}},
                        {{"name": "Bank Overdraft", "amount": 136.10, "lender": "Multiple Banks", "security": "secured"}}
                    ]
                }},
                "unsecured_debt": {{
                    "total": 243.10,
                    "details": [
                        {{"name": "Current maturities of long-term debt", "amount": 200.00, "lender": "Banks/FIs", "security": "unsecured"}},
                        {{"name": "Unsecured loan from related party", "amount": 43.10, "lender": "Related Party", "security": "unsecured"}}
                    ]
                }}
            }},
            "other_liabilities": {{
                "current_trade_payables": 2150.30,
                "non_current_trade_payables": 2000.00,
                "current_provisions": 517.70,
                "non_current_provisions": 400.00,
                "deferred_tax_liabilities": 1500.00,
                "other_current_liabilities": 850.00,
                "other_non_current_liabilities": 300.00,
                "others": []
            }}
        }}

        🎯 FINAL REMINDERS:
        - ALWAYS find unit declaration and return in currency_unit field
        - ALWAYS use Company columns when both Group and Company are present
        - ALWAYS format dates as DD/MM/YYYY
        - ALWAYS extract ENDING YEAR from date ranges (2024 from "2023-24")
        - ALWAYS extract DETAILED INDIVIDUAL VALUES from notes OR balance sheet - NEVER return null
        - debt_equity_analysis LONG-TERM: Only "Non-current borrowings" (exclude other non-current liabilities)
        - debt_equity_analysis SHORT-TERM: Only "Current borrowings" + "Current maturities" (exclude other current liabilities)
        - OTHERS: All other current and non-current liabilities (exclude borrowings)
        - If notes not available, extract from balance sheet line items
        - Focus on current year data from Company column only
        - Extract all numbers exactly as they appear in the document WITHOUT conversion

        Find the unit declaration and return it in currency_unit field!
        Extract borrowings separately from other liabilities for debt_equity_analysis!
        Never return null - extract from balance sheet if notes unavailable!
        Extract raw numbers without any mathematical conversion!
        """

    async def extract_debt_equity_data_with_ai_conversion(self, primary_images: List[str], notes_images: Dict[str, List[str]], 
                                                        currency: str, statement_type: str) -> Dict:
        """🌍 UNIVERSAL AI CONVERSION: Extract debt-equity data with intelligent header context"""
        print(f"🌍 Step 2: Universal AI-powered extraction with intelligent header context from {statement_type}...")
        
        # Step 1: Get header context first
        header_context = await self.intelligent_header_context_detection(primary_images, statement_type)
        self._current_header_context = header_context  # Store for prompt generation
        
        # Get important notes adaptively
        important_notes = await self.get_important_notes_adaptive(primary_images, statement_type)
        
        # Get AI conversion prompt with header context
        extraction_prompt = self.get_ai_conversion_prompt(statement_type, currency, important_notes)
        
        try:
            # Prepare messages for OpenAI
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": extraction_prompt}
                    ]
                }
            ]
            
            # Add primary statement images
            for i, image_path in enumerate(primary_images):
                image_base64 = self.encode_image_base64(image_path)
                messages[0]["content"].extend([
                    {"type": "text", "text": f"\n{statement_type} Page {i + 1}:"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}",
                            "detail": "high"
                        }
                    }
                ])
            
            # Add smart-selected notes images
            notes_added = 0
            for note_num in important_notes:
                if note_num in notes_images and notes_added < 8:
                    for i, image_path in enumerate(notes_images[note_num][:2]):
                        image_base64 = self.encode_image_base64(image_path)
                        messages[0]["content"].extend([
                            {"type": "text", "text": f"\nSmart-Selected Note {note_num} Page {i + 1}:"},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                        notes_added += 1
            
            logger.info(f"🌍 Universal processing: {len(primary_images)} {statement_type} images + {notes_added} note images")
            logger.info(f"🧠 Header context: {header_context.get('declaration_found', 'N/A')}")
            
            # Call OpenAI API with enhanced settings for accuracy
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                response_format={"type": "json_object"}  # Ensure JSON response
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            extraction_data = json.loads(response_text)
            
            logger.info(f"✅ Universal debt-equity data extracted from {statement_type}")
            return extraction_data
            
        except Exception as e:
            logger.error(f"Error extracting debt-equity data from {statement_type}: {e}")
            return {}

    def safe_percentage_calculation(self, numerator: Optional[float], denominator: Optional[float]) -> Optional[float]:
        """Safely calculate percentage with null handling"""
        if numerator is None or denominator is None or denominator == 0:
            return None

        try:
            return round(float(numerator) / float(denominator), 4)
        except (ValueError, TypeError, ZeroDivisionError):
            return None

    def build_final_output_clean(self, extraction_data: Dict, currency: str, year: int) -> Dict:
        """Build final output with ONLY 2 fields - Universal header context already applied"""
        print(f"🔍 Step 3: Building clean final output for year {year} (Universal header context already applied)...")
        
        try:
            final_data = FinalDebtEquityData()
            
            # Extract company info
            company_info = extraction_data.get('company_info', {})
            document_year = year
            
            # Extract financial data - Universal header context already applied
            equity_data = extraction_data.get('equity', {})
            long_term_data = extraction_data.get('long_term_debt', {})
            short_term_data = extraction_data.get('short_term_debt', {})
            
            # Get actual values - Universal header context already applied
            equity_actual = self.safe_numeric_value(equity_data.get('total_equity'))
            long_term_actual = self.safe_numeric_value(long_term_data.get('total_value'))
            short_term_actual = self.safe_numeric_value(short_term_data.get('total_value'))
            
            # Calculate percentages
            total_capital = 0
            if equity_actual: total_capital += equity_actual
            if long_term_actual: total_capital += long_term_actual
            if short_term_actual: total_capital += short_term_actual
            
            if total_capital > 0:
                equity_pct = self.safe_percentage_calculation(equity_actual, total_capital)
                long_term_pct = self.safe_percentage_calculation(long_term_actual, total_capital)
                short_term_pct = self.safe_percentage_calculation(short_term_actual, total_capital)
            else:
                equity_pct = long_term_pct = short_term_pct = None
            
            # Build debt_equity_analysis
            analysis_item = {
                "equity": {
                    "percentage": equity_pct,
                    "value": equity_actual
                },
                "long_term": {
                    "percentage": long_term_pct,
                    "value": long_term_actual
                },
                "short_term": {
                    "percentage": short_term_pct,
                    "value": short_term_actual
                },
                "year": document_year,
                "currency_unit": extraction_data.get('currency_unit', '')
            }
            
            final_data.debt_equity_analysis.append(analysis_item)
            
            # Build equity_liability section - Universal header context already applied
            final_data.equity_liability["date"] = company_info.get('date', '')
            final_data.equity_liability["currency_unit"] = extraction_data.get('currency_unit', '')
            
            # Process share capital with face value calculation
            share_capital = equity_data.get('share_capital', {})
            if share_capital and (share_capital.get('shares') or share_capital.get('value')):
                # Extract raw values
                raw_shares = self.safe_numeric_value(share_capital.get('shares'))
                face_value_per_share = self.safe_numeric_value(share_capital.get('face_value_per_share'))

                # Calculate value = no_of_shares × face_value_per_share (then apply unit conversion to result)
                calculated_value = None
                if raw_shares and face_value_per_share:
                    # no_of_shares is always actual count (no conversion needed)
                    # face_value_per_share might need conversion if it's in document units
                    from currency_handler import get_multiplier, convert_value
                    multiplier = get_multiplier(extraction_data.get('currency_unit', ''))

                    # Calculate: shares × face_value = total value
                    total_value = raw_shares * face_value_per_share

                    # Apply unit conversion to the calculated total value if needed
                    if multiplier:
                        calculated_value = convert_value(total_value, multiplier)
                    else:
                        calculated_value = total_value

                final_data.equity_liability["equity"]["equity_share_capital"] = [{
                    "name": share_capital.get('name', '') or '',
                    "no_of_shares": raw_shares,  # Actual share count - no conversion
                    "value": calculated_value
                }]
            else:
                final_data.equity_liability["equity"]["equity_share_capital"] = [{
                    "name": "",
                    "no_of_shares": None,
                    "value": None
                }]
            
            # Process other equity - Universal header context already applied
            other_equity = equity_data.get('other_equity', [])
            processed_other_equity = []
            if other_equity:
                for item in other_equity:
                    if item and isinstance(item, dict):
                        oe_actual = self.safe_numeric_value(item.get('value'))
                        
                        processed_other_equity.append({
                            "name": item.get('name', '') or '',
                            "value": oe_actual
                        })
            
            if not processed_other_equity:
                processed_other_equity = [{"name": "", "value": None}]
            
            final_data.equity_liability["equity"]["other_equity"] = processed_other_equity
            
            # Process long-term debt - Universal header context already applied
            secured_debt = long_term_data.get('secured_debt', {})
            unsecured_debt = long_term_data.get('unsecured_debt', {})
            
            # Senior debt (secured)
            senior_details = []
            if secured_debt and secured_debt.get('details'):
                for item in secured_debt['details']:
                    if item and isinstance(item, dict):
                        sd_actual = self.safe_numeric_value(item.get('amount'))
                        
                        senior_details.append({
                            "name": item.get('name', '') or '',
                            "type": item.get('security', '') or '',
                            "value": sd_actual
                        })
            
            if not senior_details:
                senior_details = [{"name": "", "type": "", "value": None}]
            
            secured_total_actual = self.safe_numeric_value(secured_debt.get('total'))
            
            final_data.equity_liability["long_term_debt"]["senior_debt"] = {
                "investor_details": senior_details,
                "value": secured_total_actual
            }
            
            # Junior debt (unsecured)
            junior_details = []
            if unsecured_debt and unsecured_debt.get('details'):
                for item in unsecured_debt['details']:
                    if item and isinstance(item, dict):
                        jd_actual = self.safe_numeric_value(item.get('amount'))
                        
                        junior_details.append({
                            "name": item.get('name', '') or '',
                            "type": 'unsecured',
                            "value": jd_actual
                        })
            
            if not junior_details:
                junior_details = [{"name": "", "type": "", "value": None}]
            
            unsecured_total_actual = self.safe_numeric_value(unsecured_debt.get('total'))
            
            final_data.equity_liability["long_term_debt"]["junior_debt"] = {
                "investor_details": junior_details,
                "value": unsecured_total_actual
            }
            
            final_data.equity_liability["long_term_debt"]["interest"] = long_term_data.get('interest_rates', '') or ''
            
            # Process short-term debt - Universal header context already applied
            st_borrowings_actual = self.safe_numeric_value(short_term_data.get('current_borrowings'))
            st_maturities_actual = self.safe_numeric_value(short_term_data.get('current_maturities'))

            # Short-term senior debt (secured) - Extract detailed breakdown
            st_secured_debt = short_term_data.get('secured_debt', {})
            st_senior_details = []

            if st_secured_debt and st_secured_debt.get('details'):
                for item in st_secured_debt['details']:
                    if item and isinstance(item, dict):
                        st_sd_actual = self.safe_numeric_value(item.get('amount'))

                        st_senior_details.append({
                            "name": item.get('name', '') or '',
                            "type": item.get('security', 'secured') or 'secured',
                            "value": st_sd_actual
                        })

            # Fallback: if no detailed breakdown, use current_borrowings total
            if not st_senior_details and st_borrowings_actual:
                st_senior_details = [{
                    "name": "Current borrowings",
                    "type": "secured",
                    "value": st_borrowings_actual
                }]

            if not st_senior_details:
                st_senior_details = [{"name": "", "type": "", "value": None}]

            # Calculate total secured debt value
            st_secured_total_actual = self.safe_numeric_value(st_secured_debt.get('total')) or st_borrowings_actual

            final_data.equity_liability["short_term_debt"]["senior_debt"] = {
                "investor_details": st_senior_details,
                "value": st_secured_total_actual
            }

            # Short-term junior debt (unsecured) - Extract detailed breakdown
            st_unsecured_debt = short_term_data.get('unsecured_debt', {})
            st_junior_details = []

            if st_unsecured_debt and st_unsecured_debt.get('details'):
                for item in st_unsecured_debt['details']:
                    if item and isinstance(item, dict):
                        st_jd_actual = self.safe_numeric_value(item.get('amount'))

                        st_junior_details.append({
                            "name": item.get('name', '') or '',
                            "type": item.get('security', 'unsecured') or 'unsecured',
                            "value": st_jd_actual
                        })

            # Fallback: if no detailed breakdown, use current_maturities total
            if not st_junior_details and st_maturities_actual:
                st_junior_details = [{
                    "name": "Current maturities of long-term debt",
                    "type": "unsecured",
                    "value": st_maturities_actual
                }]

            if not st_junior_details:
                st_junior_details = [{"name": "", "type": "", "value": None}]

            # Calculate total unsecured debt value
            st_unsecured_total_actual = self.safe_numeric_value(st_unsecured_debt.get('total')) or st_maturities_actual

            final_data.equity_liability["short_term_debt"]["junior_debt"] = {
                "investor_details": st_junior_details,
                "value": st_unsecured_total_actual
            }
            
            final_data.equity_liability["short_term_debt"]["interest"] = short_term_data.get('interest_rates', '') or ''
            
            # Process other liabilities with aggregation - Universal header context already applied
            other_liab = extraction_data.get('other_liabilities', {})
            others_list = []
            aggregated_items = {}
            other_liabilities_total = 0

            if other_liab:
                # First pass: aggregate current and non-current items of same type
                for name, value in other_liab.items():
                    if name != 'others' and value:
                        actual_value = self.safe_numeric_value(value)
                        if actual_value:
                            # Determine base name (remove current/non_current prefix)
                            if name.startswith('current_'):
                                base_name = name[8:]  # Remove 'current_'
                            elif name.startswith('non_current_'):
                                base_name = name[12:]  # Remove 'non_current_'
                            else:
                                base_name = name

                            # Aggregate values for same base type
                            if base_name in aggregated_items:
                                aggregated_items[base_name] += actual_value
                            else:
                                aggregated_items[base_name] = actual_value

                # Second pass: create final list with specific items and catch-all
                for base_name, total_value in aggregated_items.items():
                    if base_name in ['trade_payables', 'provisions', 'deferred_tax_liabilities']:
                        # Add specific known liability types
                        others_list.append({
                            "name": base_name.replace('_', ' ').title(),
                            "value": total_value
                        })
                    else:
                        # Add to catch-all "Other Liabilities"
                        other_liabilities_total += total_value

                # Add catch-all "Other Liabilities" if there are any
                if other_liabilities_total > 0:
                    others_list.append({
                        "name": "Other Liabilities",
                        "value": other_liabilities_total
                    })

            if not others_list:
                others_list = [{"name": "", "value": None}]

            final_data.equity_liability["others"]["others"] = others_list
            
            logger.info(f"✅ Clean final output built successfully for year {year} - Universal header context applied")
            
            # Return ONLY the 2 required fields
            return {
                "debt_equity_analysis": final_data.debt_equity_analysis,
                "equity_liability": final_data.equity_liability
            }
            
        except Exception as e:
            logger.error(f"Error building final output for year {year}: {e}")
            
            # Return null structure for failed processing
            return {
                "debt_equity_analysis": [{
                    "equity": {"percentage": None, "value": None},
                    "long_term": {"percentage": None, "value": None},
                    "short_term": {"percentage": None, "value": None},
                    "year": year,
                    "currency_unit": ""
                }],
                "equity_liability": {
                    "date": "",
                    "currency_unit": "",
                    "equity": {
                        "equity_share_capital": [{"name": "", "no_of_shares": None, "value": None}],
                        "other_equity": [{"name": "", "value": None}],
                        "roe": None
                    },
                    "long_term_debt": {
                        "interest": "",
                        "junior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        },
                        "senior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        }
                    },
                    "others": {
                        "others": [{"name": "", "value": None}]
                    },
                    "short_term_debt": {
                        "interest": "",
                        "junior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        },
                        "senior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        }
                    }
                }
            }

    async def process_single_file_adaptive(self, manifest_path: str) -> Dict:
        """🌍 UNIVERSAL MAIN PROCESSING: Intelligent header-context detection for all countries"""
        print("🌍 Starting Universal Processing with Intelligent Header-Context Detection...")
        
        try:
            # Load manifest
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            print(f"📄 Processing: {manifest.get('document_name', 'Unknown')}")
            
            
            classifications = manifest.get('classifications', {})
            primary_statement_type, primary_statement_info = self.get_primary_financial_statement(classifications)
            
            if not primary_statement_type:
                logger.warning("❌ No financial statements found - returning null structure")
                return {
                    "debt_equity_analysis": [{
                        "equity": {"percentage": None, "value": None},
                        "long_term": {"percentage": None, "value": None},
                        "short_term": {"percentage": None, "value": None},
                        "year": 2025,
                      
                    }, {  "currency_unit": ""}],
                    "equity_liability": {
                        "date": "",
                        "currency_unit": "",
                        "equity": {
                            "equity_share_capital": [{"name": "", "no_of_shares": None, "value": None}],
                            "other_equity": [{"name": "", "value": None}],
                            "roe": None
                        },
                        "long_term_debt": {
                            "interest": "",
                            "junior_debt": {
                                "investor_details": [{"name": "", "type": "", "value": None}],
                                "value": None
                            },
                            "senior_debt": {
                                "investor_details": [{"name": "", "type": "", "value": None}],
                                "value": None
                            }
                        },
                        "others": {
                            "others": [{"name": "", "value": None}]
                        },
                        "short_term_debt": {
                            "interest": "",
                            "junior_debt": {
                                "investor_details": [{"name": "", "type": "", "value": None}],
                                "value": None
                            },
                            "senior_debt": {
                                "investor_details": [{"name": "", "type": "", "value": None}],
                                "value": None
                            }
                        }
                    }
                }
            
            primary_images = primary_statement_info['full_paths']
            # 📅 STEP 0: AI-based year detection from document content
            document_year = await self.intelligent_year_detection(primary_images, primary_statement_type)
            
            # Show adaptive strategy result
            if primary_statement_type != "Statement of Balance Sheet":
                print(f"⚠️  Balance Sheet not available - using adaptive fallback strategy")
                print(f"🎯 Primary source: {primary_statement_type}")
            else:
                print(f"✅ Primary source: {primary_statement_type} (preferred)")
            
            # Get notes images
            notes_index = manifest.get('notes_index', {})
            notes_classification = classifications.get('Notes to Financial Statements', {})
            notes_images = {}
            
            # Map notes to image paths
            for note_num, note_pages in notes_index.items():
                note_images_list = []
                for page_info in note_pages:
                    page_number = page_info['page_number']
                    # Find corresponding classified image
                    for img_path in notes_classification.get('full_paths', []):
                        if f"page_{page_number:04d}" in img_path:
                            note_images_list.append(img_path)
                            break
                
                if note_images_list:
                    notes_images[note_num] = note_images_list
            
            print(f"📝 Available notes: {list(notes_images.keys())}")
            
            # Step 1: Enhanced currency detection with header context
            currency = await self.detect_currency_only(primary_images, primary_statement_type)
            
            # Step 2: Universal AI-powered extraction with intelligent header context
            extraction_data = await self.extract_debt_equity_data_with_ai_conversion(
                primary_images, notes_images, currency, primary_statement_type
            )
            
            # Step 3: Build clean final output (Universal header context already applied)
            final_result = self.build_final_output_clean(
                extraction_data, currency, document_year
            )
            
            # Save results
            output_dir = Path(manifest_path).parent
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_path = output_dir / f"debt_equity_result_universal_{document_year}_{timestamp}.json"
            
            with open(result_path, 'w') as f:
                json.dump(final_result, f, indent=2)
            
            logger.info(f"✅ Universal processing completed for year {document_year}. Results saved to: {result_path}")
            print(f"🎉 Universal processing completed for year {document_year}!")
            print(f"📅 AI-detected year: {document_year} (from document content)") 
            print(f"🌍 Intelligent header context detection applied for all countries!")
            print(f"🧠 No hardcoded assumptions - works with any currency/unit combination!")
            
            return final_result
            
        except Exception as e:
            logger.error(f"Error in universal processing: {e}")
            
            # Return null structure on error
            return {
                "debt_equity_analysis": [{
                    "equity": {"percentage": None, "value": None},
                    "long_term": {"percentage": None, "value": None},
                    "short_term": {"percentage": None, "value": None},
                    "year": 2024,
                    "currency_unit": ""
                }],
                "equity_liability": {
                    "date": "",
                    "currency_unit": "",
                    "equity": {
                        "equity_share_capital": [{"name": "", "no_of_shares": None, "value": None}],
                        "other_equity": [{"name": "", "value": None}],
                        "roe": None
                    },
                    "long_term_debt": {
                        "interest": "",
                        "junior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        },
                        "senior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        }
                    },
                    "others": {
                        "others": [{"name": "", "value": None}]
                    },
                    "short_term_debt": {
                        "interest": "",
                        "junior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        },
                        "senior_debt": {
                            "investor_details": [{"name": "", "type": "", "value": None}],
                            "value": None
                        }
                    }
                }
            }

    # Keep the original method for backward compatibility
    async def process_single_file(self, manifest_path: str) -> Dict:
        """Original method - now calls universal version"""
        return await self.process_single_file_adaptive(manifest_path)


async def main():
    """Main function with universal processing"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Process financial document with universal header-context detection")
    parser.add_argument("manifest_path", help="Path to the enhanced_vllm_manifest.json file")
    
    args = parser.parse_args()
    
    try:
        processor = SingleFileDebtEquityProcessor()
        result = await processor.process_single_file_adaptive(args.manifest_path)
        
        print("\n" + "="*80)
        print("🌍 UNIVERSAL OUTPUT (Intelligent Header Context)")
        print("="*80)
        
        # Print debt-equity analysis
        debt_analysis = result.get('debt_equity_analysis', [])
        if debt_analysis and debt_analysis[0]:
            analysis = debt_analysis[0]
            print(f"📊 Debt-Equity Analysis for {analysis.get('year', 'N/A')}:")
            print("-" * 60)
            
            equity = analysis.get('equity', {})
            long_term = analysis.get('long_term', {})
            short_term = analysis.get('short_term', {})
            
            if equity.get('percentage') is not None:
                print(f"Equity: {equity.get('percentage', 0):.4f} ({equity.get('percentage', 0)*100:.2f}%)")
                print(f"  • Value: {equity.get('value', 'N/A'):,}")
                print()
                
                print(f"Long-term Debt: {long_term.get('percentage', 0):.4f} ({long_term.get('percentage', 0)*100:.2f}%)")
                print(f"  • Value: {long_term.get('value', 'N/A'):,}")
                print()
                
                print(f"Short-term Debt: {short_term.get('percentage', 0):.4f} ({short_term.get('percentage', 0)*100:.2f}%)")
                print(f"  • Value: {short_term.get('value', 'N/A'):,}")
                print()
            else:
                print("Equity: null (no data extracted)")
                print("Long-term Debt: null (no data extracted)")
                print("Short-term Debt: null (no data extracted)")
        
        # Print date from equity_liability
        equity_liability = result.get('equity_liability', {})
        if equity_liability.get('date'):
            print(f"📅 Date: {equity_liability['date']}")
        
        print("\n✅ Universal processing completed!")
        print("🌍 Intelligent header context detection applied")
        print("🧠 Works with any currency/unit combination")
        print("🎯 No hardcoded assumptions!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        exit(1)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())