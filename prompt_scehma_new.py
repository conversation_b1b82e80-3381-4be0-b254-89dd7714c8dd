assets_schema = {
  "current_assets": {
    "asset_type": [
      {"name": "cash_and_cash_equivalents", "value": ""},
      {"name": "inventory", "value": ""},
      {"name": "other_assets", "value": ""},
      {"name": "trade_receivable", "value": ""}
    ]
  },
  "date": "",
  "other_non_current_assets": {
    "asset_type": [
      {"name": "capital_work_in_progress", "value": ""},
      {"name": "intangible_assets", "value": ""},
      {"name": "financial_assets", "value": ""},
      {"name": "other_non_current_assets", "value": ""},
      {"name": "deferred_tax", "value": ""},
      {"name": "<place_holder_name>", "value": ""}
    ]
  },
  "property_plant_equipment": {
    "asset_type": [
      {"name": "property_plant_equipment", "value": ""}
    ]
  },
    "currency_unit": ""
}

assets_prompt = f"""
Please extract asset-related data for the company using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated values.
2. If both company and group data are available, use **only the company** data.

3.**UNIT DETECTION**: currency unit detection for 'currency_unit' field in the schema : 
   Search for unit declarations in the document headers if they are present, such as:
      -  "(All amounts are in ₹ Crores, unless otherwise specified)" → extract "Crores"
      -  "Figures in ₹ Lakhs" → extract "Lakhs"
      -  "Amount in Millions" → extract "Millions"
      -  "Currency in ₹ Thousands" → extract "Thousands"
      -  Extract and retain all numbers as they appear in the document without any conversion.
      -  Look for the unit names like  Crores, Lakhs, Millions, Thousands, Billions to get the unit name for 'currency_unit' field, if you can not find the unit name you should consider 'null' for the currency_unit.
      -  If no specific unit declaration is explicitly mentioned in the document, For 'currency_unit' field consider it as null,should not assume or make up units by yourself.


4. If the data for a specific year has been restated or revised, that year's data should be excluded from extraction.
5. Output must strictly follow the **JSON structure provided below**, keeping all field names and hierarchy intact.
6. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.Please return the empty schema as it is provided in the prompt.if the data is not available(Please do not forget this point).
7. All "value" fields must be returned as **numbers**, not strings. if a value is unavailable, use null — do not use empty strings or placeholder text.
8. In the date field, return only in this format DD/MM/YYYY (eg: 31/12/2024 for the 31st of December 2024).
9. If any extra fields are present only under **other_non_current_assets** section, they should be added to the schema with the name present in the field in the palce of the `<place_holder_name>` and value as the value is present for that field.
10, current asset section values should come under the **current_assets** section and other non current asset section values should come under the **other_non_current_assets** section.Please do not mix the values of current and non current assets.Be mindful.
{assets_schema}
"""

cashflows_schema = {
  "cashflows": [
    {
      "capex": "",
      "cash_from_operations": "",
      "free_cash_flow": "",
      "year": "",
      "currency_unit": ""
    },
    {"currency_unit": ""}
  ]
}

cashflows_prompt = f"""
Please extract capital expenditure and related data for the **company** based on the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore group or consolidated data.
2. If both company and group data are available, use **only the company** data.

3.**UNIT DETECTION**: currency unit detection for 'currency_unit' field in the schema : 
   Search for unit declarations in the document headers if they are present, such as:
      -  "(All amounts are in ₹ Crores, unless otherwise specified)" → extract "Crores"
      -  "Figures in ₹ Lakhs" → extract "Lakhs"
      -  "Amount in  Millions" → extract "Millions"
      -  "Currency in ₹ Thousands" → extract "Thousands"
      -  Extract and retain all numbers as they appear in the document without any conversion.
      -  Look for the unit names like  Crores, Lakhs, Millions, Thousands, Billions to get the unit name for 'currency_unit' field, if you can not find the unit name you should consider 'null' for the currency_unit.
      -  If no specific unit declaration is explicitly mentioned in the document, For 'currency_unit' field consider it as null,should not assume or make up units by yourself.

4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6. Return only the year value from the date — ignore the month and day.
7. All "value" fields must be returned as **numbers**, not strings. if a value is unavailable, use null — do not use empty strings or placeholder text.
8. you should not put the - (negative sighn) in the value field.
9. free_cash_flow is calculated as cash_from_operations minus capex.
10. Extract the year part from the given date string and return it as a number (integer). The year should not contain any month or day information.
     - For example:
      * For the input "2025-07-23", return the number 2025.(Ensure that the returned year is in numeric format and does not contain any string characters.)

Here is the schema you must follow:
{cashflows_schema}
"""
schema_revenue_expenses = {
  "revenue_expenses": [
    {
      "expenses": {
        "corporate_tax": "",
        "depreciation": "",
        "exceptions_before_tax": "",
        "fuel_expenses": "",
        "interest": "",
        "employee_benifits_expenses": "",
        "other_expenses": ""
      },
      "revenue": {
        "other_income": "",
        "revenue_operations": ""
      },
      "year": "",
      "currency_unit": ""
    },

  ]
}

revenue_expenses_prompt = f"""
Please extract revenue and expense details for the **company** using the schema provided below.

**Important Instructions**:
1. Extract only the **company-specific data**. Ignore any group or consolidated values.
2. If both company and group data are available, use **only the company** data.

3.**UNIT DETECTION**: currency unit detection for 'currency_unit' field in the schema : 
   Search for unit declarations in the document headers if they are present, such as:
      -  "(All amounts are in ₹ Crores, unless otherwise specified)" → extract "Crores"
      -  "Figures in ₹ Lakhs" → extract "Lakhs"
      -  "Amount in Millions" → extract "Millions"
      -  "Currency in ₹ Thousands" → extract "Thousands"
      -  Extract and retain all numbers as they appear in the document without any conversion.
      -  Look for the unit names like  Crores, Lakhs, Millions, Thousands, Billions to get the unit name for 'currency_unit' field, if you can not find the unit name you should consider 'null' for the currency_unit.
      -  If no specific unit declaration is explicitly mentioned in the document, For 'currency_unit' field consider it as null,should not assume or make up units by yourself.

4. If data for two different years is provided, extract and return both years' data, formatted according to the specified schema for each year.
   However, if the data for a particular year is clearly marked as restated or revised, that year's data should be excluded from extraction.
   If the data is direct (i.e., not restated or revised), it can be extracted.
5. Please return the **JSON structure** provided below as it is provided, keeping all field names and hierarchy intact.if the data is not available(Please do not forget this point).
6. All "value" fields must be returned as **numbers**, not strings. f a value is unavailable, use null — do not use empty strings or placeholder text.
7. Do not put the - (negative sighn) in the value field.
8. Extract the year part from the given date string and return it as a number (integer). The year should not contain any month or day information.
     - For example:
      * For the input "2025-07-23", return the number 2025.(Ensure that the returned year is in numeric format and does not contain any string characters.)
Here is the schema you must follow:
{schema_revenue_expenses}
"""

credit_rating_schema = {
  "credit_rating": [
    {
      "agency": "",
      "name": "",
      "yearwise_rating": [
        {"rating": "", "rating_trunc": "", "year": ""},
      ]
    }
  ],
  "credit_rating_note": "",
  "currency": ""
}

credit_rating_prompt = f"""
Your task is to extract the **year-wise credit rating** for each credit rating agency based on the column titled **"Original Credit Rating / Outlook"**.

**Instructions**:
1. Use the instrument with the **highest rated amount** or **most significant exposure** per agency when multiple instruments are listed.
2. For the `credit_rating_note`, extract the insstrument type on which the rating is based.  
   For example, if ratings are based on a **Long Term Loan Facility**, then the note should be:  
   `"Credit ratings displayed below are for Long Term Loan Facility."`
3. Extract the **currency** mentioned in the credit rating section (e.g., INR, USD, etc.).
4. Should not omit any keys—if data is unavailable, use an empty string ("") or null as appropriate, and do not infer or assume values.please return the empty schema as it is provided in the prompt if the data is not available.
5. Return only the year value from the date — ignore the month and day
6.Extract year-wise credit ratings exactly as per the schema; if any field is missing or unavailable, set its value to an empty string or null, do not guess, assume, You should not use placeholder values like "Agency Name" or "AAA", and return only the empty string if the data is not available.

Here is the schema you must follow:
{credit_rating_schema}
"""